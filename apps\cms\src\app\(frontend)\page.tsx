'use client'

import React from 'react'
import { fileURLToPath } from 'url'
import { useAuth } from '@/hooks/useAuth'
import LoginForm from '@/components/LoginForm'
import AdminDashboard from '@/components/AdminDashboard'
import './styles.css'
import '@/components/LoginForm.css'
import '@/components/AdminDashboard.css'

export default function HomePage() {
  const { user, loading, error, isAuthenticated, login, logout, clearError } = useAuth()

  // Get admin URL - in production this would be from config
  const adminUrl = '/admin'
  const fileURL = `vscode://file/${fileURLToPath(import.meta.url)}`

  const handleLoginSuccess = (userData: any) => {
    // The login hook already updates the state, so we don't need to do anything here
    console.log('Login successful:', userData)
  }

  const handleLoginError = (errorMessage: string) => {
    console.error('Login error:', errorMessage)
  }

  const handleLogout = () => {
    logout()
  }

  // Show loading state
  if (loading) {
    return (
      <div className="home">
        <div className="content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h1>Loading...</h1>
          </div>
        </div>
      </div>
    )
  }

  // Show admin dashboard if authenticated
  if (isAuthenticated && user) {
    return (
      <AdminDashboard
        user={user}
        onLogout={handleLogout}
        adminUrl={adminUrl}
      />
    )
  }

  // Show login form if not authenticated
  return (
    <div className="home">
      <div className="content">
        <div className="logo-section">
          <img
            alt="Payload Logo"
            height={65}
            src="https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-favicon.svg"
            width={65}
          />
          <h1>Encreasl CMS</h1>
          <p>Content Management System</p>
        </div>

        <LoginForm
          onLoginSuccess={handleLoginSuccess}
          onError={handleLoginError}
        />

        <div className="links">
          <a
            className="docs"
            href="https://payloadcms.com/docs"
            rel="noopener noreferrer"
            target="_blank"
          >
            Documentation
          </a>
        </div>
      </div>
      <div className="footer">
        <p>Update this page by editing</p>
        <a className="codeLink" href={fileURL}>
          <code>app/(frontend)/page.tsx</code>
        </a>
      </div>
    </div>
  )
}
