'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { SidebarProps } from '@/types';
import { SidebarItem } from '@/components/ui';

/**
 * Sidebar component with navigation items
 *
 * @param isOpen - Whether the sidebar is currently open
 * @param onToggle - Function to toggle sidebar state
 */
export function Sidebar({ isOpen, onToggle: _onToggle }: SidebarProps) {
  const pathname = usePathname();
  return (
    <aside 
      className={`fixed left-0 bg-white border-r border-gray-200 transition-all duration-300 overflow-y-auto z-40 ${
        isOpen ? 'w-64' : 'w-16'
      }`} 
      style={{
        height: 'calc(100vh - 4rem)',
        scrollbarWidth: 'thin',
        scrollbarColor: '#cbd5e1 transparent'
      }}
    >
      <div className="p-3">
        <nav className="space-y-1">
          {/* Main Navigation */}
          <div className="space-y-1">
            <SidebarItem
              icon="home"
              label="Home"
              active={pathname === '/'}
              collapsed={!isOpen}
              href="/"
            />
            <SidebarItem
              icon="shorts"
              label="Shorts"
              active={pathname === '/shorts'}
              collapsed={!isOpen}
              href="/shorts"
            />
            <SidebarItem
              icon="subscriptions"
              label="Subscriptions"
              active={pathname === '/subscriptions'}
              collapsed={!isOpen}
              href="/subscriptions"
            />
          </div>

          {isOpen && <hr className="my-3 border-gray-200" />}

          {/* You section */}
          <div className="space-y-1">
            {isOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">You</div>}
            <SidebarItem
              icon="history"
              label="History"
              active={pathname === '/history'}
              collapsed={!isOpen}
              href="/history"
            />
            <SidebarItem
              icon="playlists"
              label="Playlists"
              active={pathname === '/playlists'}
              collapsed={!isOpen}
              href="/playlists"
            />
            <SidebarItem
              icon="watch-later"
              label="Watch later"
              active={pathname === '/watch-later'}
              collapsed={!isOpen}
              href="/watch-later"
            />
            <SidebarItem
              icon="liked"
              label="Liked videos"
              active={pathname === '/liked-videos'}
              collapsed={!isOpen}
              href="/liked-videos"
            />
          </div>

          {isOpen && <hr className="my-3 border-gray-200" />}

          {/* Explore section */}
          <div className="space-y-1">
            {isOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Explore</div>}
            <SidebarItem icon="trending" label="Trending" collapsed={!isOpen} />
            <SidebarItem icon="music" label="Music" collapsed={!isOpen} />
            <SidebarItem icon="gaming" label="Gaming" collapsed={!isOpen} />
            <SidebarItem icon="news" label="News" collapsed={!isOpen} />
            <SidebarItem icon="sports" label="Sports" collapsed={!isOpen} />
          </div>

          {isOpen && <hr className="my-3 border-gray-200" />}

          {/* Business Tools */}
          <div className="space-y-1">
            {isOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Business Tools</div>}
            <SidebarItem icon="analytics" label="Analytics" collapsed={!isOpen} />
            <SidebarItem icon="dashboard" label="Dashboard" collapsed={!isOpen} />
            <SidebarItem icon="reports" label="Reports" collapsed={!isOpen} />
            <SidebarItem icon="marketing" label="Marketing Hub" collapsed={!isOpen} />
            <SidebarItem icon="ecommerce" label="E-commerce" collapsed={!isOpen} />
          </div>

          {isOpen && <hr className="my-3 border-gray-200" />}

          {/* Management */}
          <div className="space-y-1">
            {isOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Management</div>}
            <SidebarItem icon="team" label="Team Management" collapsed={!isOpen} />
            <SidebarItem icon="projects" label="Projects" collapsed={!isOpen} />
            <SidebarItem icon="calendar" label="Calendar" collapsed={!isOpen} />
            <SidebarItem icon="tasks" label="Tasks" collapsed={!isOpen} />
            <SidebarItem icon="workflow" label="Workflow" collapsed={!isOpen} />
          </div>

          {isOpen && <hr className="my-3 border-gray-200" />}

          {/* Growth & Strategy */}
          <div className="space-y-1">
            {isOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Growth & Strategy</div>}
            <SidebarItem icon="growth" label="Growth Analytics" collapsed={!isOpen} />
            <SidebarItem icon="strategy" label="Strategy Planning" collapsed={!isOpen} />
            <SidebarItem icon="innovation" label="Innovation Lab" collapsed={!isOpen} />
            <SidebarItem icon="leadership" label="Leadership" collapsed={!isOpen} />
            <SidebarItem icon="consulting" label="Consulting" collapsed={!isOpen} />
          </div>

          {isOpen && <hr className="my-3 border-gray-200" />}

          {/* Settings & Support */}
          <div className="space-y-1">
            {isOpen && <div className="px-3 py-2 text-sm font-medium text-gray-900">Settings & Support</div>}
            <SidebarItem icon="settings" label="Settings" collapsed={!isOpen} />
            <SidebarItem icon="help" label="Help & Support" collapsed={!isOpen} />
            <SidebarItem icon="feedback" label="Send Feedback" collapsed={!isOpen} />
            <SidebarItem icon="notifications" label="Notifications" collapsed={!isOpen} />
            <SidebarItem icon="profile" label="Profile" collapsed={!isOpen} />
            <SidebarItem icon="billing" label="Billing" collapsed={!isOpen} />
            <SidebarItem icon="integrations" label="Integrations" collapsed={!isOpen} />
            <SidebarItem icon="api" label="API Access" collapsed={!isOpen} />
            <SidebarItem icon="security" label="Security" collapsed={!isOpen} />
            <SidebarItem icon="backup" label="Backup & Restore" collapsed={!isOpen} />
          </div>
        </nav>
      </div>
    </aside>
  );
}
