:root {
  --font-mono: 'Roboto Mono', monospace;
}

* {
  box-sizing: border-box;
}

html {
  font-size: 18px;
  line-height: 32px;

  background: rgb(0, 0, 0);
  -webkit-font-smoothing: antialiased;
}

html,
body,
#app {
  height: 100%;
}

body {
  font-family: system-ui;
  font-size: 18px;
  line-height: 32px;

  margin: 0;
  color: rgb(1000, 1000, 1000);

  @media (max-width: 1024px) {
    font-size: 15px;
    line-height: 24px;
  }
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

h1 {
  margin: 40px 0;
  font-size: 64px;
  line-height: 70px;
  font-weight: bold;

  @media (max-width: 1024px) {
    margin: 24px 0;
    font-size: 42px;
    line-height: 42px;
  }

  @media (max-width: 768px) {
    font-size: 38px;
    line-height: 38px;
  }

  @media (max-width: 400px) {
    font-size: 32px;
    line-height: 32px;
  }
}

p {
  margin: 24px 0;

  @media (max-width: 1024px) {
    margin: calc(var(--base) * 0.75) 0;
  }
}

a {
  color: currentColor;

  &:focus {
    opacity: 0.8;
    outline: none;
  }

  &:active {
    opacity: 0.7;
    outline: none;
  }
}

svg {
  vertical-align: middle;
}

.home {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;

  @media (max-width: 400px) {
    padding: 1rem;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
    width: 100%;
    max-width: 500px;
    gap: 2rem;

    h1 {
      text-align: center;
      color: white;
      margin: 0;
    }
  }

  .logo-section {
    text-align: center;
    margin-bottom: 1rem;

    img {
      margin-bottom: 1rem;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: white;
      margin: 0 0 0.5rem 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
      color: rgba(255, 255, 255, 0.9);
      font-size: 1.125rem;
      margin: 0;
    }
  }

  .loading-container {
    text-align: center;
    color: white;

    h1 {
      margin-top: 1rem;
      font-size: 1.5rem;
    }
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .links {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-top: 1rem;

    a {
      text-decoration: none;
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.2s ease;
    }

    .docs {
      color: white;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }

    .docs:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-1px);
    }
  }

  .footer {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 2rem;
    color: rgba(255, 255, 255, 0.8);

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 6px;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }

    .codeLink {
      text-decoration: none;
      padding: 0.25rem 0.75rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.9);
      font-family: var(--font-mono);
      font-size: 0.875rem;
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;
    }

    .codeLink:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}
