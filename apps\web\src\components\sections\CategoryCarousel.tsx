import React, { useState } from 'react';
import { CategoryCarouselProps } from '@/types';
import { CategoryCircle } from '@/components/ui';

/**
 * CategoryCarousel component for displaying and selecting categories
 * 
 * @param categories - Array of category names (optional, uses default if not provided)
 * @param activeCategory - Currently active category (optional, defaults to "All")
 * @param onCategoryChange - Callback when category selection changes
 */
export function CategoryCarousel({ 
  categories = [
    "All", "Business", "Technology", "Marketing", "Analytics",
    "E-commerce", "Growth", "Strategy", "Innovation", "Leadership"
  ],
  activeCategory = "All",
  onCategoryChange 
}: CategoryCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const itemWidth = 140; // 64px circle + 48px gap + padding
  const containerWidth = 5 * itemWidth; // Show about 5 items
  const maxScroll = Math.max(0, (categories.length * itemWidth) - containerWidth);
  const maxIndex = Math.max(0, Math.ceil(maxScroll / itemWidth));

  const scrollLeft = () => {
    setCurrentIndex(Math.max(0, currentIndex - 1));
  };

  const scrollRight = () => {
    setCurrentIndex(Math.min(maxIndex, currentIndex + 1));
  };

  const handleCategoryClick = (category: string) => {
    if (onCategoryChange) {
      onCategoryChange(category);
    }
  };

  return (
    <div className="relative">
      {/* Left Arrow */}
      {currentIndex > 0 && (
        <button
          onClick={scrollLeft}
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
          aria-label="Scroll left"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      )}

      {/* Right Arrow */}
      {currentIndex < maxIndex && (
        <button
          onClick={scrollRight}
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
          aria-label="Scroll right"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}

      {/* Carousel Container */}
      <div className="overflow-hidden px-6">
        <div
          className="flex transition-transform duration-300 ease-in-out py-6"
          style={{
            transform: `translateX(-${currentIndex * 140}px)`,
            gap: '48px'
          }}
        >
          {categories.map((category) => (
            <div
              key={category}
              className="flex-shrink-0"
            >
              <CategoryCircle
                label={category}
                active={activeCategory === category}
                onClick={() => handleCategoryClick(category)}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Dots Indicator */}
      {maxIndex > 0 && (
        <div className="flex justify-center space-x-2 pb-2">
          {Array.from({ length: maxIndex + 1 }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                currentIndex === index ? 'bg-blue-500' : 'bg-gray-300'
              }`}
              aria-label={`Go to page ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
