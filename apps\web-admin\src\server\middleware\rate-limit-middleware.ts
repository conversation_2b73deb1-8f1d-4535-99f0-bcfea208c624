/**
 * @file apps/web-admin/src/server/middleware/rate-limit-middleware.ts
 * @description Rate limiting middleware for admin server actions
 */

import 'server-only';
import type { ServerActionResult } from '../types/admin-server-types';

// ========================================
// TYPES
// ========================================

export type AdminRateLimitConfig = {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (...args: unknown[]) => string;
  skipCondition?: (...args: unknown[]) => boolean;
};

export type RateLimitResult = {
  allowed: boolean;
  retryAfter?: number;
  remaining?: number;
  resetTime?: number;
};

// ========================================
// RATE LIMIT STORAGE
// ========================================

// In-memory storage for rate limiting (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Clean up expired rate limit entries
 */
function cleanupExpiredEntries() {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Check rate limit for a given key
 */
async function checkAdminRateLimit(
  key: string,
  config: AdminRateLimitConfig
): Promise<RateLimitResult> {
  cleanupExpiredEntries();
  
  const now = Date.now();
  const windowStart = now;
  const windowEnd = now + config.windowMs;
  
  const existing = rateLimitStore.get(key);
  
  if (!existing || now > existing.resetTime) {
    // First request in window or window expired
    rateLimitStore.set(key, { count: 1, resetTime: windowEnd });
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: windowEnd,
    };
  }
  
  if (existing.count >= config.maxRequests) {
    // Rate limit exceeded
    return {
      allowed: false,
      retryAfter: Math.ceil((existing.resetTime - now) / 1000),
      remaining: 0,
      resetTime: existing.resetTime,
    };
  }
  
  // Increment count
  existing.count++;
  rateLimitStore.set(key, existing);
  
  return {
    allowed: true,
    remaining: config.maxRequests - existing.count,
    resetTime: existing.resetTime,
  };
}

// ========================================
// RATE LIMIT MIDDLEWARE
// ========================================

/**
 * Basic admin rate limiting middleware
 */
export function withAdminRateLimit<T extends unknown[], R>(
  key: string,
  config: AdminRateLimitConfig
) {
  return (fn: (...args: T) => Promise<R>) => {
    return async (...args: T): Promise<R> => {
      // Check skip condition
      if (config.skipCondition && config.skipCondition(...args)) {
        return fn(...args);
      }
      
      // Generate dynamic key
      const rateLimitKey = config.keyGenerator 
        ? config.keyGenerator(...args)
        : key;
      
      const rateLimitResult = await checkAdminRateLimit(rateLimitKey, config);
      
      if (!rateLimitResult.allowed) {
        const error = new Error(
          `Admin rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`
        );
        error.name = 'AdminRateLimitError';
        throw error;
      }
      
      return fn(...args);
    };
  };
}

/**
 * Admin rate limiting middleware that returns ServerActionResult
 */
export function withAdminRateLimitResult<T extends unknown[], R>(
  key: string,
  config: AdminRateLimitConfig
) {
  return (fn: (...args: T) => Promise<ServerActionResult<R>>) => {
    return async (...args: T): Promise<ServerActionResult<R>> => {
      // Check skip condition
      if (config.skipCondition && config.skipCondition(...args)) {
        return fn(...args);
      }
      
      // Generate dynamic key
      const rateLimitKey = config.keyGenerator 
        ? config.keyGenerator(...args)
        : key;
      
      const rateLimitResult = await checkAdminRateLimit(rateLimitKey, config);
      
      if (!rateLimitResult.allowed) {
        return {
          success: false,
          error: {
            code: 'ADMIN_RATE_LIMIT_EXCEEDED',
            message: `Too many admin requests. Please try again in ${rateLimitResult.retryAfter} seconds.`,
            details: {
              retryAfter: rateLimitResult.retryAfter,
              limit: config.maxRequests,
              window: config.windowMs,
            },
            timestamp: new Date().toISOString(),
          },
        };
      }
      
      return fn(...args);
    };
  };
}

// ========================================
// PREDEFINED ADMIN RATE LIMITERS
// ========================================

/**
 * User management operations rate limiter
 */
export const withUserManagementRateLimit = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) => withAdminRateLimit('admin-user-management', {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 30, // Max 30 user operations per minute
})(fn);

/**
 * Admin user operations rate limiter
 */
export const withAdminUserRateLimit = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) => withAdminRateLimit('admin-user-ops', {
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 10, // Max 10 admin user operations per 5 minutes
})(fn);

/**
 * Audit log operations rate limiter
 */
export const withAuditLogRateLimit = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) => withAdminRateLimit('admin-audit-logs', {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 50, // Max 50 audit log requests per minute
})(fn);

/**
 * Dashboard operations rate limiter
 */
export const withDashboardRateLimit = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) => withAdminRateLimit('admin-dashboard', {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // Max 100 dashboard requests per minute
})(fn);

/**
 * System settings rate limiter
 */
export const withSystemSettingsRateLimit = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) => withAdminRateLimit('admin-system-settings', {
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 5, // Max 5 system setting changes per 5 minutes
})(fn);

// ========================================
// ADMIN-SPECIFIC RATE LIMITING
// ========================================

/**
 * Admin ID-based rate limiting middleware
 */
export function withAdminIdRateLimit<T extends unknown[], R>(
  baseKey: string,
  config: AdminRateLimitConfig
) {
  return withAdminRateLimit<T, R>(baseKey, {
    ...config,
    keyGenerator: (...args) => {
      // Extract admin ID from arguments or auth context
      // This would typically be injected by auth middleware
      const adminId = 'admin-123'; // Placeholder
      return `${baseKey}:admin:${adminId}`;
    },
  });
}

/**
 * IP-based admin rate limiting middleware
 */
export function withAdminIpRateLimit<T extends unknown[], R>(
  baseKey: string,
  config: AdminRateLimitConfig
) {
  return withAdminRateLimit<T, R>(baseKey, {
    ...config,
    keyGenerator: () => {
      // In a real implementation, extract IP from headers
      // const headers = await headers();
      // const ip = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';
      // return `${baseKey}:ip:${ip}`;
      return `${baseKey}:ip:unknown`;
    },
  });
}

/**
 * Resource-based rate limiting middleware
 */
export function withResourceRateLimit<T extends unknown[], R>(
  baseKey: string,
  config: AdminRateLimitConfig,
  resourceExtractor: (...args: T) => string
) {
  return withAdminRateLimit<T, R>(baseKey, {
    ...config,
    keyGenerator: (...args) => {
      const resourceId = resourceExtractor(...args);
      return `${baseKey}:resource:${resourceId}`;
    },
  });
}

// ========================================
// COMPOSITE MIDDLEWARE
// ========================================

/**
 * Combine multiple admin rate limiters
 */
export function withMultipleAdminRateLimits<T extends unknown[], R>(
  limiters: Array<(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>>
) {
  return (fn: (...args: T) => Promise<R>) => {
    return limiters.reduce((wrappedFn, limiter) => limiter(wrappedFn), fn);
  };
}
