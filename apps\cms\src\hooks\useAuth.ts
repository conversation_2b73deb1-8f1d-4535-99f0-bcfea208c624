'use client'

import { useState, useEffect, useCallback } from 'react'

interface User {
  id: number
  email: string
  firstName?: string
  lastName?: string
  role: 'super-admin' | 'admin' | 'editor' | 'viewer'
  isActive?: boolean
  lastLogin?: string
}

interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
  isAuthenticated: boolean
}

interface LoginCredentials {
  email: string
  password: string
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
    isAuthenticated: false,
  })

  // Check if user is already authenticated on mount
  const checkAuth = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))
      
      const response = await fetch('/api/users/me', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        setAuthState({
          user: data.user,
          loading: false,
          error: null,
          isAuthenticated: true,
        })
      } else {
        // User is not authenticated
        setAuthState({
          user: null,
          loading: false,
          error: null,
          isAuthenticated: false,
        })
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setAuthState({
        user: null,
        loading: false,
        error: 'Failed to check authentication status',
        isAuthenticated: false,
      })
    }
  }, [])

  // Login function
  const login = useCallback(async (credentials: LoginCredentials): Promise<User> => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch('/api/users/login', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Login failed')
      }

      const user = data.user
      setAuthState({
        user,
        loading: false,
        error: null,
        isAuthenticated: true,
      })

      return user
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        isAuthenticated: false,
      }))
      throw error
    }
  }, [])

  // Logout function
  const logout = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      await fetch('/api/users/logout', {
        method: 'POST',
        credentials: 'include',
      })

      setAuthState({
        user: null,
        loading: false,
        error: null,
        isAuthenticated: false,
      })
    } catch (error) {
      console.error('Logout failed:', error)
      // Even if logout fails on server, clear local state
      setAuthState({
        user: null,
        loading: false,
        error: 'Logout failed',
        isAuthenticated: false,
      })
    }
  }, [])

  // Clear error function
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }))
  }, [])

  // Check authentication on mount
  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return {
    ...authState,
    login,
    logout,
    clearError,
    checkAuth,
  }
}

// Hook for getting user permissions
export function useUserPermissions(user: User | null) {
  const canCreate = user?.role === 'super-admin' || user?.role === 'admin' || user?.role === 'editor'
  const canEdit = user?.role === 'super-admin' || user?.role === 'admin' || user?.role === 'editor'
  const canDelete = user?.role === 'super-admin' || user?.role === 'admin'
  const canManageUsers = user?.role === 'super-admin' || user?.role === 'admin'
  const canManageSettings = user?.role === 'super-admin'
  const isAdmin = user?.role === 'super-admin' || user?.role === 'admin'
  const isSuperAdmin = user?.role === 'super-admin'

  return {
    canCreate,
    canEdit,
    canDelete,
    canManageUsers,
    canManageSettings,
    isAdmin,
    isSuperAdmin,
  }
}
