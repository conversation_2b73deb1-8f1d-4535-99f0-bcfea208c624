/**
 * @file apps/web-admin/src/server/middleware/validation-middleware.ts
 * @description Validation middleware for admin server actions
 */

import 'server-only';
import { z } from 'zod';
import type { ServerActionResult } from '../types/admin-server-types';

// ========================================
// TYPES
// ========================================

export type AdminValidationError = {
  field: string;
  message: string;
  code: string;
};

export type AdminValidationOptions = {
  stripUnknown?: boolean;
  allowPartial?: boolean;
  customErrorMessage?: string;
  transformErrors?: (errors: z.ZodError) => AdminValidationError[];
  requireAdminContext?: boolean;
};

export type AdminValidationResult<T> = {
  success: boolean;
  data?: T;
  errors?: AdminValidationError[];
};

// ========================================
// VALIDATION UTILITIES
// ========================================

/**
 * Transform Zod errors to admin validation error format
 */
function transformZodErrors(zodError: z.ZodError): AdminValidationError[] {
  return zodError.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));
}

/**
 * Validate data against a Zod schema with admin-specific options
 */
export function validateAdminData<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  options: AdminValidationOptions = {}
): AdminValidationResult<T> {
  try {
    const parseOptions: z.ParseParams = {};
    
    if (options.stripUnknown) {
      // Strip unknown properties
      parseOptions.errorMap = (issue, ctx) => {
        if (issue.code === z.ZodIssueCode.unrecognized_keys) {
          return { message: 'Unknown properties will be ignored' };
        }
        return { message: ctx.defaultError };
      };
    }
    
    const validatedData = schema.parse(data, parseOptions);
    
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = options.transformErrors 
        ? options.transformErrors(error)
        : transformZodErrors(error);
      
      return {
        success: false,
        errors,
      };
    }
    
    return {
      success: false,
      errors: [{
        field: 'unknown',
        message: options.customErrorMessage || 'Admin validation failed',
        code: 'ADMIN_VALIDATION_ERROR',
      }],
    };
  }
}

// ========================================
// VALIDATION MIDDLEWARE
// ========================================

/**
 * Basic admin validation middleware for server actions
 */
export function withAdminValidation<T, Args extends unknown[], R>(
  schema: z.ZodSchema<T>,
  options: AdminValidationOptions = {}
) {
  return (fn: (validatedData: T, ...args: Args) => Promise<R>) => {
    return async (data: unknown, ...args: Args): Promise<R> => {
      const validation = validateAdminData(schema, data, options);
      
      if (!validation.success) {
        const error = new Error('Admin validation failed');
        error.name = 'AdminValidationError';
        (error as any).validationErrors = validation.errors;
        throw error;
      }
      
      return fn(validation.data!, ...args);
    };
  };
}

/**
 * Admin validation middleware that returns ServerActionResult
 */
export function withAdminValidationResult<T, Args extends unknown[], R>(
  schema: z.ZodSchema<T>,
  options: AdminValidationOptions = {}
) {
  return (fn: (validatedData: T, ...args: Args) => Promise<ServerActionResult<R>>) => {
    return async (data: unknown, ...args: Args): Promise<ServerActionResult<R>> => {
      const validation = validateAdminData(schema, data, options);
      
      if (!validation.success) {
        return {
          success: false,
          error: {
            code: 'ADMIN_VALIDATION_ERROR',
            message: options.customErrorMessage || 'Invalid admin input data',
            details: { errors: validation.errors },
            timestamp: new Date().toISOString(),
          },
        };
      }
      
      return fn(validation.data!, ...args);
    };
  };
}

/**
 * Multiple admin validation middleware for complex operations
 */
export function withMultipleAdminValidation<T1, T2, Args extends unknown[], R>(
  schema1: z.ZodSchema<T1>,
  schema2: z.ZodSchema<T2>,
  options: AdminValidationOptions = {}
) {
  return (fn: (data1: T1, data2: T2, ...args: Args) => Promise<R>) => {
    return async (data1: unknown, data2: unknown, ...args: Args): Promise<R> => {
      const validation1 = validateAdminData(schema1, data1, options);
      const validation2 = validateAdminData(schema2, data2, options);
      
      const allErrors = [
        ...(validation1.errors || []),
        ...(validation2.errors || []),
      ];
      
      if (allErrors.length > 0) {
        const error = new Error('Admin validation failed');
        error.name = 'AdminValidationError';
        (error as any).validationErrors = allErrors;
        throw error;
      }
      
      return fn(validation1.data!, validation2.data!, ...args);
    };
  };
}

/**
 * Conditional admin validation middleware
 */
export function withConditionalAdminValidation<T, Args extends unknown[], R>(
  condition: (data: unknown, ...args: Args) => boolean,
  schema: z.ZodSchema<T>,
  options: AdminValidationOptions = {}
) {
  return (fn: (data: T | unknown, ...args: Args) => Promise<R>) => {
    return async (data: unknown, ...args: Args): Promise<R> => {
      if (condition(data, ...args)) {
        const validation = validateAdminData(schema, data, options);
        
        if (!validation.success) {
          const error = new Error('Admin validation failed');
          error.name = 'AdminValidationError';
          (error as any).validationErrors = validation.errors;
          throw error;
        }
        
        return fn(validation.data!, ...args);
      }
      
      return fn(data, ...args);
    };
  };
}

// ========================================
// ADMIN-SPECIFIC SANITIZATION
// ========================================

/**
 * Admin input sanitization middleware
 */
export function withAdminSanitization<T extends Record<string, unknown>, Args extends unknown[], R>(
  sanitizers: Partial<Record<keyof T, (value: any) => any>>
) {
  return (fn: (sanitizedData: T, ...args: Args) => Promise<R>) => {
    return async (data: T, ...args: Args): Promise<R> => {
      const sanitizedData = { ...data };
      
      for (const [key, sanitizer] of Object.entries(sanitizers)) {
        if (key in sanitizedData && sanitizer) {
          sanitizedData[key as keyof T] = sanitizer(sanitizedData[key as keyof T]);
        }
      }
      
      return fn(sanitizedData, ...args);
    };
  };
}

/**
 * Admin string sanitization middleware with enhanced security
 */
export function withAdminStringSanitization<Args extends unknown[], R>(
  fn: (...args: Args) => Promise<R>
) {
  return async (...args: Args): Promise<R> => {
    const sanitizedArgs = args.map(arg => {
      if (typeof arg === 'string') {
        return arg
          .trim()
          .replace(/[<>]/g, '') // Remove basic HTML tags
          .replace(/javascript:/gi, '') // Remove javascript: protocol
          .replace(/on\w+=/gi, '') // Remove event handlers
          .replace(/data:/gi, '') // Remove data URLs
          .replace(/vbscript:/gi, '') // Remove VBScript
          .substring(0, 10000); // Limit length
      }
      
      if (typeof arg === 'object' && arg !== null) {
        return sanitizeAdminObject(arg);
      }
      
      return arg;
    }) as Args;
    
    return fn(...sanitizedArgs);
  };
}

/**
 * Recursively sanitize admin object properties with enhanced security
 */
function sanitizeAdminObject(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(sanitizeAdminObject);
  }
  
  if (typeof obj === 'object' && obj !== null) {
    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(obj)) {
      // Sanitize the key itself
      const sanitizedKey = key
        .replace(/[<>]/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+=/gi, '')
        .substring(0, 100);
      
      if (typeof value === 'string') {
        sanitized[sanitizedKey] = value
          .trim()
          .replace(/[<>]/g, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+=/gi, '')
          .replace(/data:/gi, '')
          .replace(/vbscript:/gi, '')
          .substring(0, 10000);
      } else {
        sanitized[sanitizedKey] = sanitizeAdminObject(value);
      }
    }
    
    return sanitized;
  }
  
  return obj;
}

// ========================================
// PERMISSION-BASED VALIDATION
// ========================================

/**
 * Validation middleware that checks admin permissions
 */
export function withPermissionValidation<T, Args extends unknown[], R>(
  schema: z.ZodSchema<T>,
  requiredPermissions: string[],
  options: AdminValidationOptions = {}
) {
  return (fn: (validatedData: T, ...args: Args) => Promise<R>) => {
    return async (data: unknown, ...args: Args): Promise<R> => {
      // First validate the data
      const validation = validateAdminData(schema, data, options);
      
      if (!validation.success) {
        const error = new Error('Admin validation failed');
        error.name = 'AdminValidationError';
        (error as any).validationErrors = validation.errors;
        throw error;
      }
      
      // Then check permissions (this would be integrated with auth middleware)
      // In a real implementation, this would check against the admin's actual permissions
      const hasPermissions = requiredPermissions.every(permission => {
        // Placeholder permission check
        return true; // Would check against actual admin permissions
      });
      
      if (!hasPermissions) {
        const error = new Error('Insufficient permissions for this validation');
        error.name = 'AdminPermissionError';
        throw error;
      }
      
      return fn(validation.data!, ...args);
    };
  };
}
