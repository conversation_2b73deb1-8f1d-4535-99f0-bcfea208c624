"use client";

import React from "react";
import { Header, Sidebar } from "@/components/layout";
import { CategoryCarousel, VideoGrid } from "@/components/sections";
import { useSidebar, useCategory } from "@/hooks";

/**
 * Home page component - Main application layout
 *
 * This component orchestrates the main layout with header, sidebar, and content areas.
 * It uses custom hooks for state management and composed components for clean separation of concerns.
 */
export default function Home() {
  const { isOpen: sidebarOpen, toggle: toggleSidebar } = useSidebar(true);
  const { activeCategory, selectCategory } = useCategory("All");

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    // TODO: Implement search functionality
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header
        sidebarOpen={sidebarOpen}
        onToggleSidebar={toggleSidebar}
        onSearch={handleSearch}
      />

      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
      />

      {/* Main Content */}
      <main className={`transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
        {/* Category Circles Carousel */}
        <div className="bg-white border-b border-gray-200">
          <CategoryCarousel
            activeCategory={activeCategory}
            onCategoryChange={selectCategory}
          />
        </div>

        {/* Video Grid */}
        <VideoGrid />
      </main>
    </div>
  );
}
