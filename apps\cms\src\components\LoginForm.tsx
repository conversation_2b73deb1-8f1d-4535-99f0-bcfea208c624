'use client'

import React, { useState } from 'react'
import { Eye, EyeOff, AlertCircle, Loader2, Shield } from 'lucide-react'

interface LoginFormProps {
  onLoginSuccess: (user: any) => void
  onError: (error: string) => void
}

interface FormData {
  email: string
  password: string
}

interface FormErrors {
  email?: string
  password?: string
  general?: string
}

export default function LoginForm({ onLoginSuccess, onError }: LoginFormProps) {
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
  })
  
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Validate form
  const validateForm = (): boolean => {
    const errors: FormErrors = {}
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }
    
    if (!formData.password) {
      errors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email.trim(),
          password: formData.password,
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || 'Login failed')
      }
      
      // Login successful
      onLoginSuccess(data.user)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed'
      setError(errorMessage)
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof FormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value,
    }))
    
    // Clear errors when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: undefined,
      }))
    }
    
    if (error) {
      setError(null)
    }
  }

  return (
    <div className="login-form-container">
      <div className="login-header">
        <div className="logo-container">
          <Shield className="logo-icon" />
        </div>
        <h1>Admin Login</h1>
        <p>Sign in to access the CMS</p>
      </div>

      <form onSubmit={handleSubmit} className="login-form">
        {/* General Error */}
        {error && (
          <div className="error-message">
            <AlertCircle className="error-icon" />
            <div>
              <p className="error-title">Authentication Failed</p>
              <p className="error-text">{error}</p>
            </div>
          </div>
        )}

        {/* Email Field */}
        <div className="form-field">
          <label htmlFor="email">Email Address</label>
          <input
            id="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            className={`form-input ${formErrors.email ? 'error' : ''} ${loading ? 'disabled' : ''}`}
            placeholder="Enter your email"
            disabled={loading}
            autoComplete="email"
          />
          {formErrors.email && (
            <p className="field-error">{formErrors.email}</p>
          )}
        </div>

        {/* Password Field */}
        <div className="form-field">
          <label htmlFor="password">Password</label>
          <div className="password-input-container">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              className={`form-input ${formErrors.password ? 'error' : ''} ${loading ? 'disabled' : ''}`}
              placeholder="Enter your password"
              disabled={loading}
              autoComplete="current-password"
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
              disabled={loading}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          {formErrors.password && (
            <p className="field-error">{formErrors.password}</p>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className={`submit-button ${loading ? 'loading' : ''}`}
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="loading-spinner" />
              Signing in...
            </>
          ) : (
            'Sign In'
          )}
        </button>
      </form>
    </div>
  )
}
