/**
 * @file apps/web-admin/src/server/middleware/index.ts
 * @description Middleware exports for the Web Admin App server
 */

// ========================================
// ADMIN AUTHENTICATION MIDDLEWARE
// ========================================

export {
  withAdminAuth,
  withOptionalAdminAuth,
  withRequiredAdminAuth,
  withSuperAdminAuth,
  withRoleAuth,
  withPermissionAuth,
  withAdminErrorHandling,
  withAdminAuditLog,
  type AdminAuthContext,
  type AdminAuthRequirement,
} from './auth-middleware';

// ========================================
// ADMIN RATE LIMITING MIDDLEWARE
// ========================================

export {
  withAdminRateLimit,
  withAdminRateLimitResult,
  withUserManagementRateLimit,
  withAdminUserRateLimit,
  withAuditLogRateLimit,
  withDashboardRateLimit,
  withSystemSettingsRateLimit,
  withAdminIdRateLimit,
  withAdminIpRateLimit,
  withResourceRateLimit,
  withMultipleAdminRateLimits,
  type AdminRateLimitConfig,
  type RateLimitResult,
} from './rate-limit-middleware';

// ========================================
// ADMIN VALIDATION MIDDLEWARE
// ========================================

export {
  withAdminValidation,
  withAdminValidationResult,
  withMultipleAdminValidation,
  withConditionalAdminValidation,
  withAdminSanitization,
  withAdminStringSanitization,
  withPermissionValidation,
  validateAdminData,
  type AdminValidationError,
  type AdminValidationOptions,
  type AdminValidationResult,
} from './validation-middleware';

// ========================================
// COMPOSITE MIDDLEWARE HELPERS
// ========================================

/**
 * Compose multiple admin middleware functions
 */
export function composeAdminMiddleware<T extends unknown[], R>(
  ...middlewares: Array<(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>>
) {
  return (fn: (...args: T) => Promise<R>) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), fn);
  };
}

/**
 * Create an admin middleware pipeline
 */
export function createAdminPipeline<T extends unknown[], R>(
  middlewares: Array<(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>>
) {
  return (fn: (...args: T) => Promise<R>) => {
    return composeAdminMiddleware(...middlewares)(fn);
  };
}

// ========================================
// COMMON ADMIN MIDDLEWARE COMBINATIONS
// ========================================

/**
 * Standard admin middleware stack for user management
 */
export function withUserManagementDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[] = ['users.read']
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAdminStringSanitization,
    withUserManagementRateLimit,
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * Admin user management middleware stack
 */
export function withAdminUserDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[] = ['admin.manage']
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAdminStringSanitization,
    withAdminUserRateLimit,
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * Audit log middleware stack
 */
export function withAuditDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[] = ['audit.read']
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAuditLogRateLimit,
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * Dashboard middleware stack
 */
export function withDashboardDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[] = ['dashboard.read']
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withDashboardRateLimit,
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * System settings middleware stack (super admin only)
 */
export function withSystemSettingsDefaults<T extends unknown[], R>(
  actionName: string
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAdminStringSanitization,
    withSystemSettingsRateLimit,
    withSuperAdminAuth
  );
}

/**
 * High-security admin middleware stack
 */
export function withHighSecurityDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[]
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAdminStringSanitization,
    withAdminIdRateLimit(actionName, {
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 5, // Very restrictive
    }),
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * Bulk operation middleware stack
 */
export function withBulkOperationDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[]
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAdminStringSanitization,
    withAdminRateLimit(`bulk-${actionName}`, {
      windowMs: 10 * 60 * 1000, // 10 minutes
      maxRequests: 3, // Very restrictive for bulk operations
    }),
    withPermissionAuth(requiredPermissions)
  );
}

// ========================================
// SPECIALIZED MIDDLEWARE COMBINATIONS
// ========================================

/**
 * Read-only admin middleware stack
 */
export function withReadOnlyDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[]
) {
  return composeAdminMiddleware<T, R>(
    withAdminErrorHandling,
    withDashboardRateLimit, // More lenient rate limiting for reads
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * Write operation admin middleware stack
 */
export function withWriteOperationDefaults<T extends unknown[], R>(
  actionName: string,
  requiredPermissions: string[]
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName), // Always log write operations
    withAdminErrorHandling,
    withAdminStringSanitization,
    withUserManagementRateLimit, // Moderate rate limiting for writes
    withPermissionAuth(requiredPermissions)
  );
}

/**
 * Critical operation admin middleware stack
 */
export function withCriticalOperationDefaults<T extends unknown[], R>(
  actionName: string
) {
  return composeAdminMiddleware<T, R>(
    withAdminAuditLog(actionName),
    withAdminErrorHandling,
    withAdminStringSanitization,
    withSystemSettingsRateLimit, // Very restrictive rate limiting
    withSuperAdminAuth // Require super admin for critical operations
  );
}
