/**
 * @file apps/web-admin/src/server/validators/audit-schemas.ts
 * @description Validation schemas for audit logging and compliance operations
 */

import { z } from 'zod';

// ========================================
// AUDIT LOG SCHEMAS
// ========================================

/**
 * Audit log entry creation schema
 */
export const CreateAuditLogSchema = z.object({
  adminId: z
    .string()
    .uuid('Invalid admin user ID'),
  
  action: z
    .string()
    .min(1, 'Action is required')
    .max(100, 'Action name too long')
    .regex(/^[a-zA-Z0-9_\.]+$/, 'Action name can only contain letters, numbers, dots, and underscores'),
  
  resource: z
    .string()
    .max(100, 'Resource name too long')
    .optional(),
  
  resourceId: z
    .string()
    .max(100, 'Resource ID too long')
    .optional(),
  
  oldValues: z
    .record(z.string(), z.unknown())
    .optional(),
  
  newValues: z
    .record(z.string(), z.unknown())
    .optional(),
  
  details: z
    .record(z.string(), z.unknown())
    .optional(),
  
  ipAddress: z
    .string()
    .ip('Invalid IP address'),
  
  userAgent: z
    .string()
    .max(500, 'User agent too long'),
  
  sessionId: z
    .string()
    .uuid('Invalid session ID')
    .optional(),
  
  timestamp: z
    .string()
    .datetime()
    .default(() => new Date().toISOString()),
  
  severity: z
    .enum(['low', 'medium', 'high', 'critical'])
    .default('medium'),
  
  category: z
    .enum([
      'authentication',
      'authorization',
      'user_management',
      'admin_management',
      'system_settings',
      'data_access',
      'data_modification',
      'security',
      'compliance',
      'other'
    ])
    .default('other'),
  
  status: z
    .enum(['success', 'failure', 'error'])
    .default('success'),
  
  errorMessage: z
    .string()
    .max(1000, 'Error message too long')
    .optional(),
  
  // Compliance fields
  complianceFlags: z
    .array(z.enum(['gdpr', 'hipaa', 'sox', 'pci_dss', 'iso27001']))
    .optional()
    .default([]),
  
  retentionPeriod: z
    .number()
    .int('Retention period must be an integer')
    .min(1, 'Retention period must be at least 1 day')
    .max(3650, 'Retention period cannot exceed 10 years')
    .optional()
    .default(2555), // 7 years default
});

/**
 * Audit log query filters schema
 */
export const AuditLogFiltersSchema = z.object({
  page: z
    .number()
    .int('Page must be an integer')
    .min(1, 'Page must be at least 1')
    .max(1000, 'Page number too high')
    .default(1),
  
  limit: z
    .number()
    .int('Limit must be an integer')
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .default(50),
  
  sortBy: z
    .enum(['timestamp', 'action', 'adminId', 'severity', 'category'])
    .default('timestamp'),
  
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc'),
  
  // Filter by admin
  adminId: z
    .string()
    .uuid('Invalid admin user ID')
    .optional(),
  
  // Filter by action
  action: z
    .string()
    .max(100, 'Action name too long')
    .optional(),
  
  // Filter by resource
  resource: z
    .string()
    .max(100, 'Resource name too long')
    .optional(),
  
  resourceId: z
    .string()
    .max(100, 'Resource ID too long')
    .optional(),
  
  // Filter by severity
  severity: z
    .array(z.enum(['low', 'medium', 'high', 'critical']))
    .optional(),
  
  // Filter by category
  category: z
    .array(z.enum([
      'authentication',
      'authorization',
      'user_management',
      'admin_management',
      'system_settings',
      'data_access',
      'data_modification',
      'security',
      'compliance',
      'other'
    ]))
    .optional(),
  
  // Filter by status
  status: z
    .array(z.enum(['success', 'failure', 'error']))
    .optional(),
  
  // Date range filters
  startDate: z
    .string()
    .datetime()
    .optional(),
  
  endDate: z
    .string()
    .datetime()
    .optional(),
  
  // IP address filter
  ipAddress: z
    .string()
    .ip('Invalid IP address')
    .optional(),
  
  // Search in details
  searchQuery: z
    .string()
    .max(200, 'Search query too long')
    .optional()
    .transform(val => val?.trim()),
  
  // Compliance filters
  complianceFlags: z
    .array(z.enum(['gdpr', 'hipaa', 'sox', 'pci_dss', 'iso27001']))
    .optional(),
});

// ========================================
// AUDIT EXPORT SCHEMAS
// ========================================

/**
 * Audit log export schema
 */
export const ExportAuditLogsSchema = z.object({
  format: z
    .enum(['csv', 'json', 'xlsx', 'pdf'])
    .default('csv'),
  
  filters: AuditLogFiltersSchema.optional(),
  
  includeFields: z
    .array(z.enum([
      'timestamp',
      'adminId',
      'action',
      'resource',
      'resourceId',
      'oldValues',
      'newValues',
      'details',
      'ipAddress',
      'userAgent',
      'severity',
      'category',
      'status',
      'errorMessage'
    ]))
    .optional(),
  
  excludeFields: z
    .array(z.enum([
      'oldValues',
      'newValues',
      'details',
      'userAgent',
      'sessionId'
    ]))
    .optional(),
  
  // Export metadata
  requestedBy: z
    .string()
    .uuid('Invalid admin user ID'),
  
  reason: z
    .string()
    .max(500, 'Reason too long')
    .optional(),
  
  // Compliance requirements
  complianceRequirement: z
    .enum(['gdpr_request', 'audit_review', 'security_investigation', 'compliance_report', 'other'])
    .optional(),
  
  // Encryption for sensitive exports
  encryptExport: z
    .boolean()
    .optional()
    .default(true),
  
  // Retention for export file
  exportRetentionDays: z
    .number()
    .int('Export retention must be an integer')
    .min(1, 'Export retention must be at least 1 day')
    .max(90, 'Export retention cannot exceed 90 days')
    .optional()
    .default(30),
});

// ========================================
// COMPLIANCE REPORT SCHEMAS
// ========================================

/**
 * Compliance report generation schema
 */
export const GenerateComplianceReportSchema = z.object({
  reportType: z
    .enum(['gdpr', 'hipaa', 'sox', 'pci_dss', 'iso27001', 'custom']),
  
  startDate: z
    .string()
    .datetime(),
  
  endDate: z
    .string()
    .datetime(),
  
  scope: z
    .enum(['all_activities', 'user_data', 'admin_activities', 'security_events', 'data_access'])
    .default('all_activities'),
  
  includeMetrics: z
    .boolean()
    .optional()
    .default(true),
  
  includeCharts: z
    .boolean()
    .optional()
    .default(true),
  
  includeRecommendations: z
    .boolean()
    .optional()
    .default(true),
  
  format: z
    .enum(['pdf', 'html', 'json'])
    .default('pdf'),
  
  // Report metadata
  requestedBy: z
    .string()
    .uuid('Invalid admin user ID'),
  
  purpose: z
    .string()
    .max(500, 'Purpose description too long')
    .optional(),
  
  // Custom report parameters
  customFilters: z
    .record(z.string(), z.unknown())
    .optional(),
  
  // Distribution
  recipients: z
    .array(z.string().email())
    .max(10, 'Too many recipients')
    .optional(),
  
  // Security
  passwordProtected: z
    .boolean()
    .optional()
    .default(true),
  
  watermark: z
    .boolean()
    .optional()
    .default(true),
});

// ========================================
// DATA RETENTION SCHEMAS
// ========================================

/**
 * Data retention policy schema
 */
export const DataRetentionPolicySchema = z.object({
  policyName: z
    .string()
    .min(1, 'Policy name is required')
    .max(100, 'Policy name too long'),
  
  description: z
    .string()
    .max(500, 'Description too long')
    .optional(),
  
  dataTypes: z
    .array(z.enum([
      'audit_logs',
      'user_data',
      'admin_data',
      'session_data',
      'export_files',
      'compliance_reports'
    ]))
    .min(1, 'At least one data type is required'),
  
  retentionPeriodDays: z
    .number()
    .int('Retention period must be an integer')
    .min(1, 'Retention period must be at least 1 day')
    .max(3650, 'Retention period cannot exceed 10 years'),
  
  autoDelete: z
    .boolean()
    .optional()
    .default(false),
  
  archiveBeforeDelete: z
    .boolean()
    .optional()
    .default(true),
  
  complianceRequirements: z
    .array(z.enum(['gdpr', 'hipaa', 'sox', 'pci_dss', 'iso27001']))
    .optional()
    .default([]),
  
  exceptions: z
    .array(z.object({
      condition: z.string().max(200),
      retentionPeriodDays: z.number().int().min(1).max(3650),
      reason: z.string().max(500),
    }))
    .optional()
    .default([]),
  
  isActive: z
    .boolean()
    .optional()
    .default(true),
  
  createdBy: z
    .string()
    .uuid('Invalid admin user ID'),
  
  approvedBy: z
    .string()
    .uuid('Invalid admin user ID')
    .optional(),
});

// ========================================
// TYPE EXPORTS
// ========================================

export type CreateAuditLogData = z.infer<typeof CreateAuditLogSchema>;
export type AuditLogFilters = z.infer<typeof AuditLogFiltersSchema>;
export type ExportAuditLogsData = z.infer<typeof ExportAuditLogsSchema>;
export type GenerateComplianceReportData = z.infer<typeof GenerateComplianceReportSchema>;
export type DataRetentionPolicyData = z.infer<typeof DataRetentionPolicySchema>;

// ========================================
// VALIDATION FUNCTIONS
// ========================================

/**
 * Validate audit log creation data
 */
export function validateCreateAuditLog(data: unknown) {
  return CreateAuditLogSchema.safeParse(data);
}

/**
 * Validate audit log filters
 */
export function validateAuditLogFilters(data: unknown) {
  return AuditLogFiltersSchema.safeParse(data);
}

/**
 * Validate audit log export request
 */
export function validateExportAuditLogs(data: unknown) {
  return ExportAuditLogsSchema.safeParse(data);
}

/**
 * Validate compliance report generation request
 */
export function validateGenerateComplianceReport(data: unknown) {
  return GenerateComplianceReportSchema.safeParse(data);
}

/**
 * Validate data retention policy
 */
export function validateDataRetentionPolicy(data: unknown) {
  return DataRetentionPolicySchema.safeParse(data);
}

// ========================================
// CUSTOM VALIDATORS
// ========================================

/**
 * Date range validator for audit queries
 */
export const auditDateRangeValidator = z
  .object({
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
  })
  .refine(
    (data) => {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      const maxRange = 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds
      
      return end > start && (end.getTime() - start.getTime()) <= maxRange;
    },
    'Date range cannot exceed 90 days and end date must be after start date'
  );

/**
 * Compliance requirement validator
 */
export const complianceRequirementValidator = z
  .array(z.string())
  .refine(
    (requirements) => {
      const validRequirements = ['gdpr', 'hipaa', 'sox', 'pci_dss', 'iso27001'];
      return requirements.every(req => validRequirements.includes(req));
    },
    'Invalid compliance requirement'
  );

// ========================================
// SCHEMA TRANSFORMERS
// ========================================

/**
 * Transform audit log data for storage
 */
export const AuditLogStorageSchema = CreateAuditLogSchema.transform((data) => ({
  ...data,
  id: crypto.randomUUID(),
  createdAt: new Date().toISOString(),
  hash: crypto.randomUUID(), // In real implementation, use proper hash
  version: 1,
  archived: false,
}));

/**
 * Transform audit log data for export
 */
export const AuditLogExportSchema = AuditLogStorageSchema.transform((data) => {
  const { hash, version, archived, ...exportData } = data;
  return {
    ...exportData,
    exportedAt: new Date().toISOString(),
  };
});
