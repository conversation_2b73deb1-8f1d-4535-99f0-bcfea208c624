/**
 * @file apps/web-admin/src/server/index.ts
 * @description Server-side exports for the Web Admin App
 * 
 * This file serves as the main entry point for all server-side functionality
 * specific to the admin application. It re-exports server actions, utilities,
 * and services that are used throughout the admin app.
 */

import 'server-only';

// ========================================
// ADMIN SERVER ACTIONS
// ========================================

// User management server actions
export {
  createUser,
  updateUser,
  deleteUser,
  getUserById,
  getUsersList,
  banUser,
  unbanUser,
  type UserManagementData,
  type UserListFilters,
} from './actions/user-management-actions';

// Admin operations server actions
export {
  createAdminUser,
  updateAdminUser,
  deleteAdminUser,
  assignAdminRole,
  revokeAdminRole,
  validateAdminPermissions,
  type AdminUserData,
  type AdminRoleAssignment,
} from './actions/admin-actions';

// Audit logging server actions
export {
  logAdminAction,
  getAuditLogs,
  exportAuditLogs,
  type AuditLogEntry,
  type AuditLogFilters,
} from './actions/audit-actions';

// Dashboard server actions
export {
  getDashboardStats,
  type DashboardStats,
} from './actions/dashboard-actions';

// ========================================
// ADMIN MIDDLEWARE
// ========================================

// Admin authentication middleware
export {
  withAdminAuth,
  withOptionalAdminAuth,
  withRequiredAdminAuth,
  withSuperAdminAuth,
  withRoleAuth,
  withPermissionAuth,
  withAdminErrorHandling,
  withAdminAuditLog,
  type AdminAuthContext,
  type AdminAuthRequirement,
} from './middleware/auth-middleware';

// Admin rate limiting middleware
export {
  withAdminRateLimit,
  withAdminRateLimitResult,
  withUserManagementRateLimit,
  withAdminUserRateLimit,
  withAuditLogRateLimit,
  withDashboardRateLimit,
  withSystemSettingsRateLimit,
  withAdminIdRateLimit,
  withAdminIpRateLimit,
  withResourceRateLimit,
  withMultipleAdminRateLimits,
  type AdminRateLimitConfig,
  type RateLimitResult,
} from './middleware/rate-limit-middleware';

// Admin validation middleware
export {
  withAdminValidation,
  withAdminValidationResult,
  withMultipleAdminValidation,
  withConditionalAdminValidation,
  withAdminSanitization,
  withAdminStringSanitization,
  withPermissionValidation,
  validateAdminData,
  type AdminValidationError,
  type AdminValidationOptions,
  type AdminValidationResult,
} from './middleware/validation-middleware';

// Composite admin middleware
export {
  composeAdminMiddleware,
  createAdminPipeline,
  withUserManagementDefaults,
  withAdminUserDefaults,
  withAuditDefaults,
  withDashboardDefaults,
  withSystemSettingsDefaults,
  withHighSecurityDefaults,
  withBulkOperationDefaults,
  withReadOnlyDefaults,
  withWriteOperationDefaults,
  withCriticalOperationDefaults,
} from './middleware';

// ========================================
// ADMIN VALIDATORS
// ========================================

// User management validators
export {
  CreateUserSchema,
  UpdateUserSchema,
  DeleteUserSchema,
  BanUserSchema,
  UnbanUserSchema,
  UserListFiltersSchema,
  UserSearchSchema,
  BulkUpdateUsersSchema,
  BulkDeleteUsersSchema,
  validateCreateUser,
  validateUpdateUser,
  validateDeleteUser,
  validateBanUser,
  validateUserListFilters,
  validateBulkUpdateUsers,
  type CreateUserData,
  type UpdateUserData,
  type DeleteUserData,
  type BanUserData,
  type UnbanUserData,
  type UserListFilters,
  type UserSearchData,
  type BulkUpdateUsersData,
  type BulkDeleteUsersData,
} from './validators/user-schemas';

// Admin user management validators
export {
  CreateAdminUserSchema,
  UpdateAdminUserSchema,
  DeleteAdminUserSchema,
  AssignRoleSchema,
  RevokeRoleSchema,
  AssignPermissionSchema,
  AdminSessionSchema,
  TerminateSessionSchema,
  AdminActivitySchema,
  AdminPreferencesSchema,
  validateCreateAdminUser,
  validateUpdateAdminUser,
  validateAssignRole,
  validateAdminSession,
  validateAdminPreferences,
  type CreateAdminUserData,
  type UpdateAdminUserData,
  type DeleteAdminUserData,
  type AssignRoleData,
  type RevokeRoleData,
  type AssignPermissionData,
  type AdminSessionData,
  type TerminateSessionData,
  type AdminActivityData,
  type AdminPreferencesData,
} from './validators/admin-schemas';

// Audit and compliance validators
export {
  CreateAuditLogSchema,
  AuditLogFiltersSchema,
  ExportAuditLogsSchema,
  GenerateComplianceReportSchema,
  DataRetentionPolicySchema,
  validateCreateAuditLog,
  validateAuditLogFilters,
  validateExportAuditLogs,
  validateGenerateComplianceReport,
  validateDataRetentionPolicy,
  type CreateAuditLogData,
  type AuditLogFilters,
  type ExportAuditLogsData,
  type GenerateComplianceReportData,
  type DataRetentionPolicyData,
} from './validators/audit-schemas';

// Common admin validators
export {
  AdminIdSchema,
  ResourceIdSchema,
  AdminActionSchema,
  AdminReasonSchema,
  AdminNotesSchema,
  AdminPaginationSchema,
  AdminSearchSchema,
  AdminDateRangeSchema,
  AdminBulkOperationSchema,
  validateAdminSchema,
  validateMultipleAdminSchemas,
  type AdminId,
  type ResourceId,
  type AdminAction,
  type AdminReason,
  type AdminNotes,
  type AdminPagination,
  type AdminSearch,
  type AdminDateRange,
  type AdminBulkOperation,
} from './validators';

// ========================================
// ADMIN SERVER UTILITIES
// ========================================

// Admin-specific server utilities
export {
  validateAdminRequest,
  checkAdminPermissions,
  sanitizeAdminInput,
  formatAdminResponse,
  handleAdminError,
} from './utils/admin-utils';

// Permission management utilities
export {
  hasPermission,
  checkResourceAccess,
  validateRolePermissions,
  type Permission,
  type ResourceAccess,
} from './utils/permissions';

// Data validation utilities
export {
  validateUserData,
  validateAdminData,
  validateSystemSettings,
  type ValidationResult,
} from './utils/validation';

// ========================================
// ADMIN SERVER SERVICES
// ========================================

// User risk assessment service (admin-specific business logic)
export {
  UserRiskAssessmentService,
  type RiskAssessment,
  type RiskFactor,
  type SecurityEvent,
  type ComplianceViolation,
} from './services/user-risk-assessment-service';

// Compliance reporting service (admin-specific compliance)
export {
  ComplianceReportingService,
  type ComplianceReport,
  type ComplianceFinding,
  type ComplianceMetrics,
  type DataSubjectRequest,
} from './services/compliance-reporting-service';

// ========================================
// ADMIN SERVER UTILITIES
// ========================================

// Data export utilities (admin-specific)
export {
  generateUserDataExport,
  generateAuditLogExport,
  formatExportData,
  type AdminExportOptions,
} from './utils/export-utils';

// ========================================
// TYPE EXPORTS
// ========================================

// Re-export common admin server types
export type {
  AdminServerActionResult,
  AdminServerError,
  AdminValidationError,
  AdminRequestContext,
} from './types/admin-server-types';
