/**
 * @file apps/web-admin/src/server/middleware/auth-middleware.ts
 * @description Authentication middleware for admin server actions
 */

import 'server-only';
import { headers } from 'next/headers';
import type { ServerActionResult } from '../types/admin-server-types';

// ========================================
// TYPES
// ========================================

export type AdminAuthContext = {
  adminId?: string;
  sessionId?: string;
  isAuthenticated: boolean;
  isSuperAdmin: boolean;
  roles: string[];
  permissions: string[];
  userAgent?: string;
  ipAddress?: string;
  timestamp: string;
  requestId: string;
};

export type AdminAuthRequirement = {
  required?: boolean;
  requireSuperAdmin?: boolean;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  allowSelfAccess?: boolean;
};

// ========================================
// AUTH UTILITIES
// ========================================

/**
 * Extract admin authentication context from request headers
 */
async function getAdminAuthContext(): Promise<AdminAuthContext> {
  const headersList = await headers();
  
  // In a real implementation, you would:
  // 1. Extract JWT token from Authorization header or session cookie
  // 2. Verify the token with Firebase Auth
  // 3. Get admin user information and permissions from Firestore
  // 4. Check if the admin is active and not suspended
  
  const authHeader = headersList.get('authorization');
  const sessionCookie = headersList.get('cookie')?.includes('admin-session=');
  
  // Placeholder implementation
  const isAuthenticated = Boolean(authHeader || sessionCookie);
  
  return {
    adminId: isAuthenticated ? 'admin-123' : undefined, // Would be extracted from verified token
    sessionId: isAuthenticated ? 'session-456' : undefined,
    isAuthenticated,
    isSuperAdmin: false, // Would be determined from admin user record
    roles: isAuthenticated ? ['admin'] : [], // Would be fetched from admin user record
    permissions: isAuthenticated ? ['users.read', 'users.update'] : [], // Would be computed from roles
    userAgent: headersList.get('user-agent') || undefined,
    ipAddress: headersList.get('x-forwarded-for') || 
               headersList.get('x-real-ip') || 
               'unknown',
    timestamp: new Date().toISOString(),
    requestId: globalThis.crypto.randomUUID(),
  };
}

/**
 * Check if admin has required permissions
 */
function hasRequiredPermissions(
  authContext: AdminAuthContext,
  requirements: AdminAuthRequirement
): boolean {
  // Super admin bypass
  if (authContext.isSuperAdmin) {
    return true;
  }
  
  // Check required roles
  if (requirements.requiredRoles && requirements.requiredRoles.length > 0) {
    const hasRole = requirements.requiredRoles.some(role => 
      authContext.roles.includes(role)
    );
    if (!hasRole) return false;
  }
  
  // Check required permissions
  if (requirements.requiredPermissions && requirements.requiredPermissions.length > 0) {
    const hasPermission = requirements.requiredPermissions.every(permission => 
      authContext.permissions.includes(permission)
    );
    if (!hasPermission) return false;
  }
  
  return true;
}

// ========================================
// MIDDLEWARE FUNCTIONS
// ========================================

/**
 * Admin authentication middleware for server actions
 */
export function withAdminAuth<T extends unknown[], R>(
  fn: (authContext: AdminAuthContext, ...args: T) => Promise<R>,
  requirements: AdminAuthRequirement = { required: true }
) {
  return async (...args: T): Promise<R> => {
    const authContext = await getAdminAuthContext();
    
    // Check authentication requirement
    if (requirements.required && !authContext.isAuthenticated) {
      throw new Error('Admin authentication required');
    }
    
    // Check super admin requirement
    if (requirements.requireSuperAdmin && !authContext.isSuperAdmin) {
      throw new Error('Super admin access required');
    }
    
    // Check permissions
    if (!hasRequiredPermissions(authContext, requirements)) {
      throw new Error('Insufficient permissions');
    }
    
    return fn(authContext, ...args);
  };
}

/**
 * Optional admin authentication middleware
 */
export function withOptionalAdminAuth<T extends unknown[], R>(
  fn: (authContext: AdminAuthContext, ...args: T) => Promise<R>
) {
  return withAdminAuth(fn, { required: false });
}

/**
 * Required admin authentication middleware
 */
export function withRequiredAdminAuth<T extends unknown[], R>(
  fn: (authContext: AdminAuthContext, ...args: T) => Promise<R>
) {
  return withAdminAuth(fn, { required: true });
}

/**
 * Super admin only middleware
 */
export function withSuperAdminAuth<T extends unknown[], R>(
  fn: (authContext: AdminAuthContext, ...args: T) => Promise<R>
) {
  return withAdminAuth(fn, { required: true, requireSuperAdmin: true });
}

/**
 * Role-based authentication middleware
 */
export function withRoleAuth<T extends unknown[], R>(
  roles: string[],
  fn: (authContext: AdminAuthContext, ...args: T) => Promise<R>
) {
  return withAdminAuth(fn, { required: true, requiredRoles: roles });
}

/**
 * Permission-based authentication middleware
 */
export function withPermissionAuth<T extends unknown[], R>(
  permissions: string[],
  fn: (authContext: AdminAuthContext, ...args: T) => Promise<R>
) {
  return withAdminAuth(fn, { required: true, requiredPermissions: permissions });
}

// ========================================
// ERROR HANDLING MIDDLEWARE
// ========================================

/**
 * Admin error handling middleware with proper error responses
 */
export function withAdminErrorHandling<T extends unknown[], R>(
  fn: (...args: T) => Promise<ServerActionResult<R>>
) {
  return async (...args: T): Promise<ServerActionResult<R>> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error('Admin server action error:', error);
      
      // Handle specific error types
      if (error instanceof Error) {
        if (error.message.includes('Admin authentication required')) {
          return {
            success: false,
            error: {
              code: 'ADMIN_AUTHENTICATION_REQUIRED',
              message: 'Admin authentication is required for this action',
              timestamp: new Date().toISOString(),
            },
          };
        }
        
        if (error.message.includes('Super admin access required')) {
          return {
            success: false,
            error: {
              code: 'SUPER_ADMIN_REQUIRED',
              message: 'Super admin access is required for this action',
              timestamp: new Date().toISOString(),
            },
          };
        }
        
        if (error.message.includes('Insufficient permissions')) {
          return {
            success: false,
            error: {
              code: 'INSUFFICIENT_PERMISSIONS',
              message: 'You do not have the required permissions for this action',
              timestamp: new Date().toISOString(),
            },
          };
        }
      }
      
      // Generic error response
      return {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          details: process.env.NODE_ENV === 'development' ? { error: String(error) } : undefined,
          timestamp: new Date().toISOString(),
        },
      };
    }
  };
}

// ========================================
// AUDIT LOGGING MIDDLEWARE
// ========================================

/**
 * Admin action audit logging middleware
 */
export function withAdminAuditLog<T extends unknown[], R>(
  actionName: string,
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    const requestId = globalThis.crypto.randomUUID();
    
    // Log action start
    console.log(`[ADMIN_AUDIT] ${actionName} started - Request ID: ${requestId}`);
    
    try {
      const result = await fn(...args);
      const duration = Date.now() - startTime;
      
      // Log successful action
      console.log(`[ADMIN_AUDIT] ${actionName} completed successfully in ${duration}ms - Request ID: ${requestId}`);
      
      // In a real implementation, save audit log to database
      // await saveAuditLog({
      //   actionName,
      //   requestId,
      //   status: 'success',
      //   duration,
      //   timestamp: new Date().toISOString(),
      //   adminId: authContext.adminId,
      //   ipAddress: authContext.ipAddress,
      //   userAgent: authContext.userAgent,
      // });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log failed action
      console.error(`[ADMIN_AUDIT] ${actionName} failed after ${duration}ms - Request ID: ${requestId}`, error);
      
      // In a real implementation, save audit log to database
      // await saveAuditLog({
      //   actionName,
      //   requestId,
      //   status: 'error',
      //   duration,
      //   error: String(error),
      //   timestamp: new Date().toISOString(),
      //   adminId: authContext.adminId,
      //   ipAddress: authContext.ipAddress,
      //   userAgent: authContext.userAgent,
      // });
      
      throw error;
    }
  };
}
