'use client'

import React from 'react'
import { 
  Shield, 
  Users, 
  FileText, 
  Image, 
  Settings, 
  LogOut, 
  User,
  ExternalLink,
  BarChart3,
  Folder
} from 'lucide-react'

interface AdminDashboardProps {
  user: any
  onLogout: () => void
  adminUrl: string
}

export default function AdminDashboard({ user, onLogout, adminUrl }: AdminDashboardProps) {
  const handleLogout = async () => {
    try {
      await fetch('/api/users/logout', {
        method: 'POST',
        credentials: 'include',
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      onLogout()
    }
  }

  const quickActions = [
    {
      title: 'Admin Panel',
      description: 'Access the full CMS admin interface',
      icon: Shield,
      href: adminUrl,
      external: true,
      color: 'blue'
    },
    {
      title: 'Manage Posts',
      description: 'Create and edit blog posts',
      icon: FileText,
      href: `${adminUrl}/collections/posts`,
      external: true,
      color: 'green'
    },
    {
      title: 'Media Library',
      description: 'Upload and manage media files',
      icon: Image,
      href: `${adminUrl}/collections/media`,
      external: true,
      color: 'purple'
    },
    {
      title: 'Services',
      description: 'Manage service offerings',
      icon: Folder,
      href: `${adminUrl}/collections/services`,
      external: true,
      color: 'orange'
    },
    {
      title: 'Users',
      description: 'Manage user accounts and permissions',
      icon: Users,
      href: `${adminUrl}/collections/users`,
      external: true,
      color: 'red',
      adminOnly: true
    },
    {
      title: 'Settings',
      description: 'Configure CMS settings',
      icon: Settings,
      href: `${adminUrl}/admin`,
      external: true,
      color: 'gray',
      adminOnly: true
    }
  ]

  const isAdmin = user?.role === 'super-admin' || user?.role === 'admin'
  const filteredActions = quickActions.filter(action => !action.adminOnly || isAdmin)

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 border-blue-200 hover:bg-blue-100 text-blue-700',
      green: 'bg-green-50 border-green-200 hover:bg-green-100 text-green-700',
      purple: 'bg-purple-50 border-purple-200 hover:bg-purple-100 text-purple-700',
      orange: 'bg-orange-50 border-orange-200 hover:bg-orange-100 text-orange-700',
      red: 'bg-red-50 border-red-200 hover:bg-red-100 text-red-700',
      gray: 'bg-gray-50 border-gray-200 hover:bg-gray-100 text-gray-700'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="admin-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-content">
          <div className="user-info">
            <div className="user-avatar">
              <User size={24} />
            </div>
            <div className="user-details">
              <h1>Welcome back, {user?.firstName || user?.email?.split('@')[0] || 'Admin'}!</h1>
              <p className="user-role">
                {user?.role?.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())} • {user?.email}
              </p>
              {!user?.isActive && (
                <span className="status-badge inactive">Account Inactive</span>
              )}
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="logout-button"
            title="Sign Out"
          >
            <LogOut size={20} />
            Sign Out
          </button>
        </div>
      </div>

      {/* Quick Actions Grid */}
      <div className="dashboard-content">
        <div className="section-header">
          <h2>Quick Actions</h2>
          <p>Access your most used CMS features</p>
        </div>

        <div className="actions-grid">
          {filteredActions.map((action) => {
            const Icon = action.icon
            return (
              <a
                key={action.title}
                href={action.href}
                target={action.external ? '_blank' : '_self'}
                rel={action.external ? 'noopener noreferrer' : undefined}
                className={`action-card ${getColorClasses(action.color)}`}
              >
                <div className="action-icon">
                  <Icon size={24} />
                </div>
                <div className="action-content">
                  <h3 className="action-title">
                    {action.title}
                    {action.external && <ExternalLink size={16} className="external-icon" />}
                  </h3>
                  <p className="action-description">{action.description}</p>
                </div>
              </a>
            )
          })}
        </div>

        {/* Stats Section */}
        <div className="stats-section">
          <div className="section-header">
            <h2>Quick Stats</h2>
            <p>Overview of your content</p>
          </div>
          
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <FileText size={20} />
              </div>
              <div className="stat-content">
                <div className="stat-value">-</div>
                <div className="stat-label">Posts</div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">
                <Image size={20} />
              </div>
              <div className="stat-content">
                <div className="stat-value">-</div>
                <div className="stat-label">Media Files</div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">
                <Folder size={20} />
              </div>
              <div className="stat-content">
                <div className="stat-value">-</div>
                <div className="stat-label">Services</div>
              </div>
            </div>
            
            {isAdmin && (
              <div className="stat-card">
                <div className="stat-icon">
                  <Users size={20} />
                </div>
                <div className="stat-content">
                  <div className="stat-value">-</div>
                  <div className="stat-label">Users</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
